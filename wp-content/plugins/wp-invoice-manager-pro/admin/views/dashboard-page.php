<?php
/**
 * Dashboard Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get managers
$invoice_manager = new SI_Invoice();
$client_manager = new SI_Client();

// Get statistics
$stats = array(
    'total' => $invoice_manager->si_count_invoices_with_filters('', ''),
    'draft' => $invoice_manager->si_count_invoices_with_filters('', 'draft'),
    'sent' => $invoice_manager->si_count_invoices_with_filters('', 'sent'),
    'paid' => $invoice_manager->si_count_invoices_with_filters('', 'paid'),
    'overdue' => $invoice_manager->si_count_invoices_with_filters('', 'overdue')
);

// Calculate total revenue (paid invoices)
$paid_invoices = $invoice_manager->si_get_invoices(array('status' => 'paid', 'limit' => -1));
$total_revenue = 0;
foreach ($paid_invoices as $invoice) {
    $total_revenue += floatval($invoice->total_amount);
}

// Get recent invoices (last 5)
$recent_invoices = $invoice_manager->si_get_invoices(array('limit' => 5));

// Get total clients
$total_clients = count($client_manager->si_get_clients());

// Get current month stats
$current_month = date('Y-m');
$monthly_invoices = $invoice_manager->si_get_invoices_with_filters('', '', $current_month . '-01', $current_month . '-31', -1, 0);
$monthly_revenue = 0;
foreach ($monthly_invoices as $invoice) {
    if ($invoice->status === 'paid') {
        $monthly_revenue += floatval($invoice->total_amount);
    }
}

// Set up page header variables
$page_title = __('Dashboard', 'wp-invoice-manager-pro');
$page_subtitle = __('Overview of your invoice management system', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-dashboard';
$header_actions = array(
    array(
        'type' => 'link',
        'url' => admin_url('admin.php?page=wimp-create-invoice'),
        'text' => __('Create Invoice', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <!-- Statistics Overview -->
    <div class="si-dashboard-stats">
        <h2><?php echo esc_html__('Overview', 'simple-invoice'); ?></h2>
        
        <div class="si-stats-grid">
            <!-- Total Revenue -->
            <div class="si-stat-card si-stat-revenue">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(si_format_currency($total_revenue)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Revenue', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('All time', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="si-stat-card si-stat-monthly">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-calendar-alt"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(si_format_currency($monthly_revenue)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('This Month', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html(date('F Y')); ?></div>
                </div>
            </div>

            <!-- Total Invoices -->
            <div class="si-stat-card si-stat-invoices">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-media-text"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html($stats['total']); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Invoices', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('All time', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Total Clients -->
            <div class="si-stat-card si-stat-clients">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-groups"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html($total_clients); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Clients', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Active clients', 'simple-invoice'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Status Breakdown -->
    <div class="si-dashboard-breakdown">
        <h2><?php echo esc_html__('Invoice Status Breakdown', 'simple-invoice'); ?></h2>
        
        <div class="si-status-stats">
            <div class="si-status-card si-status-paid p-4">
                <div class="si-status-number"><?php echo esc_html($stats['paid']); ?></div>
                <div class="si-status-label"><?php echo esc_html__('Paid', 'simple-invoice'); ?></div>
                
            </div>

            <div class="si-status-card si-status-sent">
                <div class="si-status-number"><?php echo esc_html($stats['sent']); ?></div>
                <div class="si-status-label"><?php echo esc_html__('Sent', 'simple-invoice'); ?></div>
                
            </div>

            <div class="si-status-card si-status-draft">
                <div class="si-status-number"><?php echo esc_html($stats['draft']); ?></div>
                <div class="si-status-label"><?php echo esc_html__('Draft', 'simple-invoice'); ?></div>
               
            </div>

            <div class="si-status-card si-status-overdue">
                <div class="si-status-number"><?php echo esc_html($stats['overdue']); ?></div>
                <div class="si-status-label"><?php echo esc_html__('Overdue', 'simple-invoice'); ?></div>
                
            </div>
        </div>
    </div>

    <!-- Two Column Layout -->
    <div class="si-dashboard-columns">
        <!-- Recent Invoices -->
        <div class="si-dashboard-column">
            <div class="si-dashboard-widget">
                <div class="si-widget-header">
                    <h3><?php echo esc_html__('Recent Invoices', 'simple-invoice'); ?></h3>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-invoices')); ?>" class="si-widget-action">
                        <?php echo esc_html__('View All', 'simple-invoice'); ?>
                    </a>
                </div>
                <div class="si-widget-content">
                    <?php if (!empty($recent_invoices)): ?>
                        <div class="si-recent-invoices">
                            <?php foreach ($recent_invoices as $invoice): ?>
                                <?php
                                $client = $client_manager->si_get_client($invoice->client_id);
                                $client_name = $client ? $client->name : __('Unknown Client', 'simple-invoice');
                                $amount = si_format_currency($invoice->total_amount);
                                $status_class = 'si-status-' . $invoice->status;
                                $status_label = ucfirst($invoice->status);
                                ?>
                                <div class="si-recent-invoice">
                                    <div class="si-invoice-info">
                                        <div class="si-invoice-number">
                                            <strong><?php echo esc_html($invoice->invoice_number); ?></strong>
                                        </div>
                                        <div class="si-invoice-client"><?php echo esc_html($client_name); ?></div>
                                        <div class="si-invoice-date"><?php echo esc_html(date('M j, Y', strtotime($invoice->created_at))); ?></div>
                                    </div>
                                    <div class="si-invoice-amount">
                                        <div class="si-amount"><?php echo esc_html($amount); ?></div>
                                        <span class="si-status-badge <?php echo esc_attr($status_class); ?>">
                                            <?php echo esc_html($status_label); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="si-empty-state">
                            <p><?php echo esc_html__('No invoices found. Create your first invoice to get started!', 'simple-invoice'); ?></p>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice')); ?>" class="button button-primary">
                                <?php echo esc_html__('Create Invoice', 'simple-invoice'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="si-dashboard-column">
            <div class="si-dashboard-widget">
                <div class="si-widget-header">
                    <h3><?php echo esc_html__('Quick Actions', 'simple-invoice'); ?></h3>
                </div>
                <div class="si-widget-content">
                    <div class="si-quick-actions">
                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice')); ?>" class="si-quick-action">
                            <span class="si-action-icon dashicons dashicons-plus-alt"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('Create Invoice', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('Generate a new invoice for your clients', 'simple-invoice'); ?></div>
                            </div>
                        </a>

                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients&action=add')); ?>" class="si-quick-action">
                            <span class="si-action-icon dashicons dashicons-plus"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('Add Client', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('Add a new client to your database', 'simple-invoice'); ?></div>
                            </div>
                        </a>

                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-templates')); ?>" class="si-quick-action">
                            <span class="si-action-icon dashicons dashicons-admin-appearance"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('Manage Invoice Types', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('Customize your invoice types', 'simple-invoice'); ?></div>
                            </div>
                        </a>

                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-settings')); ?>" class="si-quick-action">
                            <span class="si-action-icon dashicons dashicons-admin-settings"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('Settings', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('Configure your business information', 'simple-invoice'); ?></div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
